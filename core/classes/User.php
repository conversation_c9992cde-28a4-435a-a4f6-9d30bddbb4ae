<?php

class User extends Base {

    protected $dbTable = "users";
    protected $firstname, $lastname;
    protected $email, $mobile;
    protected $line1, $town, $postcode;
    protected $dob;
    protected $password;
    protected $isAdmin, $isManager, $isAuthor;
    protected $resetIP, $resetSession, $resetExpiry, $resetCode;
    protected $securityCode, $securityCodeExpiry;
    protected $profilePictureUrl;
    protected $bio;
    protected $lastLoggedIn;

    protected $newpassword;

    protected $teams, $teamsManaged;
    protected $invites;
    protected $stripeCustomerIDs = [];

    protected $dbFields = [
        "firstname",
        "lastname",
        "email",
        "mobile",
        "line1",
        "town",
        "postcode",
        "dob",
        "password",
        "resetIP",
        "resetSession",
        "resetExpiry",
        "resetCode",
        "isAdmin",
        "securityCode",
        "securityCodeExpiry",
        "profilePictureUrl",
        "bio",
        "lastLoggedIn",
    ];

    function __construct(Int $id = null) {
        parent::__construct($id);
    }

    function __toString() {
        return ($this->firstname || $this->lastname) ? trim("{$this->firstname} {$this->lastname}") : "{$this->email}";
    }

    function Save() {
        if (!$this->mobile) $this->mobile = null;
        // if ($this->newpassword) $this->setPassword($this->newpassword);
        if (!$this->password) $this->setPassword(sha1(md5(time())));
        $rlt = parent::Save();
        if (is_string($rlt)) return "Save failed. Possible duplicate? (Emails and Mobile numbers must be unique)";
        return $rlt;
    }

    function Name() {
        return "{$this->firstname} {$this->lastname}";
    }

    function setSecurityCode() {
        if (!$this->id) return;
        $this->securityCode = null;
        for ($i = 1; $i <= 6; $i++) {
            $this->securityCode .= ($i == 1) ? (string) rand(1, 9) : (string) rand(0, 9);
        }
        new Db("UPDATE `users` SET `securityCode` = :securityCode, `securityCodeExpiry` = :securityCodeExpiry WHERE `id` = :id", ["securityCode" => $this->securityCode, "securityCodeExpiry" => date('y-m-d H:i:s', strtotime("+30 minutes")), "id" => $this->id]);
        return $this->securityCode;
    }

    function checkSecurityCode(String $code) {
        if ($this->securityCode && $this->securityCodeExpiry && $this->securityCode == $code && time() < strtotime($this->securityCodeExpiry)) {
            $this->Activate();
            $this->updateLastLogin();
            return true;
        }
    }

    function sendSecurityCode($newUser = false) {
        if (!$this->id || !$this->securityCode) return;
        global $config;
        $message[] = "Hi {$this->firstname}";
        if ($newUser) {
            $subject = "Welcome to {$config['system']['name']}";
        } else $subject = "Password help for {$config['system']['name']}";
        $message[] = "We have received a password reminder/activation request for your account at " . $config['system']['name'];
        $message[] = "Enter activation code: <span  style=\"display: inline-block; background-color: lightblue; color: white; padding: 10px;\">
        {$this->securityCode}</span> which will give you access and allow you to reset your password.";
        $message[] = "This link will autmatically expire in 30 minutes";
        $message[] = "Many thanks";
        $message[] = $config['system']['name'];
        $to[$this->email] = $this->__toString();
        $cc = $bcc = [];
        $attachments = [];
        $emailRlt = \Email::Issue($subject, $message, $to, $cc, $bcc, $attachments);
    }

    // Email 2FA  - 2 Factor Authentication
    public function sendAuthCode() {
        if (!$this->id) return;

        $this->setSecurityCode();

        global $config;
        $message[] = "Hi {$this->firstname}";
        $subject = "2FA Code for {$config['system']['name']}";
        $message[] = "We have received a 2FA request for your account at " . $config['system']['name'];
        $message[] = "Enter 2FA code: <span  style=\"display: inline-block; background-color: lightblue; color: white; padding: 10px;\">
        {$this->securityCode}</span> to access your account.";
        $message[] = "This link will autmatically expire in 30 minutes";
        $message[] = "Many thanks";
        $message[] = $config['system']['name'];
        $to[$this->email] = $this->__toString();
        $cc = $bcc = [];
        $attachments = [];
        $emailRlt = \Email::Issue($subject, $message, $to, $cc, $bcc, $attachments);
    }

    function stripeCustomerID() {
        $sql = "SELECT `stripeCustomerID` FROM `stripeCustomers` WHERE `userID` = :userID AND deleted IS NULL";
        $db = new Db($sql, ["userID" => $this->id]);
        if ($db->rows) return $db->rows[0]['stripeCustomerID'];
    }

    /* Methods */
    function PasswordReset($minutes = 15) {
        // exit(\Tools\Dump($this));
        global $config;
        $resetCode = substr(md5(time()), 0, 20);
        $sql = "UPDATE `users` SET `resetIP` = :resetIP, `resetSession` = :resetSession, `resetCode` = :resetCode, `resetExpiry` = :resetExpiry WHERE `id` = :id AND `resetExpiry` IS NULL";
        $sqlData = [
            "resetIP" => $_SERVER['REMOTE_ADDR'],
            "resetSession" => session_id(),
            "resetCode" => $resetCode,
            "resetExpiry" => date('Y-m-d H:i:s', strtotime("+$minutes minutes")),
            "id" => $this->id
        ];
        $db = new Db($sql, $sqlData);
        /* Log it */
        $msg = __FUNCTION__ . " request for {$this} (ID {$this->id}";
        $msg = (!$db->affectedRows) ? "$msg unsuccessful" : "$msg successful";
        Logging::Add($msg);

        if (!$db->affectedRows) return (\Messaging\Add("We could not send you an activation code. You can try again or contact Support for further assistance", "warning"));
        $subject = "Password Reset Request from " . $config['system']['name'];
        $message[] = "Hi {$this->firstname}";
        $message[] = "We have received a password reset request for your account at " . $config['system']['name'];
        $message[] = "Please click <span  class=\"display: inline-block; background-color: blue; color: white; padding: 10px;\"><a href=\"{$config['system']['url']}Home/PasswordReset/{$this->email}/$resetCode\">here</a></span> to set your new password";
        $message[] = "This link will autmatically expire in $minutes minutes";
        $message[] = "Many thanks";
        $message[] = $config['system']['name'];
        $to[$this->email] = $this->__toString();
        $cc = $bcc = [];
        $attachments = [];
        $emailRlt = \Email::Issue($subject, $message, $to, $cc, $bcc, $attachments);
        if ($emailRlt) return (\Messaging\Add("We have sent you a Password Reminder email with an activation code", "success"));
        \Messaging\Add($emailRlt, "warning");
    }

    function CaptainResignationConfirm(Team $team) {
        global $config;
        $coordinator = $team->getCoordinator();
        $subject = "Captain Resignation $team";
        $message[] = "Hi {$this->firstname}";
        $message[] = "We have initiated a process to replace your captaincy (as {$this->email}) of $team in " . $team->getLeague();
        $message[] = "If you did not request this, please get in touch with your coordinator as soon as possible";
        $message[] = "{$coordinator} {$coordinator->email}";
        $message[] = "Many thanks";
        $message[] = $config['system']['organisation'];
        $to[$this->email] = $this->__toString();
        $cc = [$coordinator->email => $coordinator->__toString()];
        $bcc = [];
        $attachments = [];
        return \Email::Issue($subject, $message, $to, $cc, $bcc, $attachments);
    }

    function CaptainInvitation(Team $team) {
        global $config;
        $coordinator = $team->getCoordinator();
        $subject = "Captain Invitation for $team";
        $message[] = "Hi {$this->firstname}";
        $message[] = "We have initiated a process to make you captain (as {$this->email}) of $team in " . $team->getLeague();
        $message[] = "If you did not request this, please get in touch with your coordinator as soon as possible";
        $message[] = "{$coordinator} {$coordinator->email}";
        $message[] = "You can view and manage your team via the Admin screen on the leagues4you lockeroom at <a href=\"https://lockerroom.leagues4you.co.uk/\">https://lockerroom.leagues4you.co.uk</a>. If you are not sure of your login details, there is a password reminder function there to get you started";
        $message[] = "Many thanks";
        $message[] = $config['system']['organisation'];
        $to[$this->email] = $this->__toString();
        $cc = [$coordinator->email => $coordinator->__toString()];
        $bcc = [];
        $attachments = [];
        $docType = DocLibraryType::FromName('Terms and Conditions');
        $terms = DocLibraryVersion::Fetch($docType);
        $attachmentStrings[] = [
            'data' => $terms->filedata,
            'name' => 'Terms_and_Conditions.pdf',

        ];
        return \Email2::Issue($subject, $message, $to, $cc, $bcc, $attachments, $attachmentStrings);
    }

    function ClearReset() {
        $sql = "UPDATE `users` SET `resetIP` = NULL, `resetSession` = NULL, `resetCode` = NULL, `resetExpiry` = NULL WHERE `id` = :id";
        $db = new Db($sql, ["id" => $this->id]);
        $this->resetIP = null;
        $this->resetSession = null;
        $this->resetCode = null;
        $this->resetExpiry = null;
    }

    function Activate() {
        if (!$this->isActivated() && $this->id) {
            // return new Db(
            //     "UPDATE `users` SET `activationIP` = :activationIP, `activationStamp` = NOW() WHERE `id` = :id",
            //     ["id" => $this->id, "activationIP" => $_SERVER['REMOTE_ADDR']]
            // );
            $this->activationIP = $_SERVER['REMOTE_ADDR'];
            $this->activationStamp = date('Y-m-d H:i:s');
        }
    }

    function ResetPassword(String $resetCode) {
        $error = null;
        if (!$this->resetIP || $_SERVER['REMOTE_ADDR'] != $this->resetIP) $error = "Cannot reset password. Requested from a different connection (IP)";
        if (!$this->resetSession || session_id() != $this->resetSession) $error = "Cannot reset password. Requested from a different connection (Session)<br>" . session_id() . "<br>{$this->resetSession}";
        if (!$this->resetExpiry || date('Y-m-d H:i:s') > $this->resetExpiry) $error = "Cannot reset password. The request has expired {$this->resetExpiry}";
        if (!$this->resetCode || $resetCode != $this->resetCode) $error = "Cannot reset password. Code incorrect";
        // $this->resetIP = $this->resetSession = $this->resetExpiry = $this->resetCode = null;
        $this->ClearReset();
        $this->Activate();
        $this->Save();
        if (!$error) {
            $this->Login();
            return true;
        }
    }

    function ChangePassword(array $data = []) {
        /* Expects $data[0] and $data[1] as Password and Confirmed */
        if (!isset($data[0]) || !isset($data[1])) return "Cannot update password. Missing password and/or confirmed password";
        if (!$data[0]) return "Supplied Password is empty";
        if (!$data[1]) return "Confirmation Password empty";
        if ($data[0] != $data[1]) return "Passwords do not match";
        $this->setPassword($data[0]);
        // $rlt = $this->Save();
        // Tools::Dump($this->db);
        // Logging::Add("{$this} (User ID {$this->id}) password updated");
        return true;
    }


    function Authenticate(String $password) {
        if ($this->deleted) return "Account Suspended";
        if (!$this->password || !password_verify($password, $this->password)) return ("Login Failed $password");
        // if (!$this->isAdmin) return("Not an Admin");
        $this->Login();
        return true;
    }

    function Login(Bool $logAction = true) {
        if ($logAction === true) Logging::Add("{$this} (ID {$this->id}) logged in");
        session_regenerate_id();
        $_SESSION['userID'] = $this->id;
    }

    protected function updateLastLogin() {
        $sql = "UPDATE `users` SET `lastLoggedIn` = NOW() WHERE `id` = :id";
        $rlt = Database::Execute($sql, ["id" => $this->id]);
        return $rlt;
    }

    function Logout() {
        // Logging::Add("{$this} (ID {$this->id}) logged out");
        unset($_SESSION['userID']);
        session_regenerate_id();
    }

    /* Checkers */
    function activated() {
        return ($this->activationStamp) ? true : false;
    }

    function isActivated() {
        return ($this->activationStamp) ? true : false;
    }

    function ApiOutput() {
        return [
            "id" => $this->id,
            "firstname" => $this->firstname,
            "lastname" => $this->lastname,
            "name" => $this->__toString(),
            "email" => $this->email,
            "mobile" => $this->mobile,
            "profilePictureUrl" => $this->profilePictureUrl,
        ];
    }

    function ApiData() {
        return [
            "id" => $this->id,
            "firstname" => $this->firstname,
            "lastname" => $this->lastname,
            "name" => $this->__toString(),
            "email" => $this->email,
            "mobile" => $this->mobile,
            "line1" => $this->line1,
            "town" => $this->town,
            "postcode" => $this->postcode,
            "dob" => $this->dob,
            "isAdmin" => $this->isAdmin,
            "isManager" => $this->isManager,
            "isActivated" => ($this->activationStamp) ? 1 : 0,
            "activationStamp" => $this->activationStamp,
            "profilePictureUrl" => $this->profilePictureUrl,
            'fullName' => "{$this->email} {$this->firstname} {$this->lastname}",
            'bio' => $this->bio,
            'lastLoggedIn' => $this->lastLoggedIn ? date("d.m.Y", strtotime($this->lastLoggedIn)) : null,
        ];
    }

    function getFollowedTeams() {
        if (!$this->teams) $this->teams = Team::User($this);
        return $this->teams;
    }

    function getManagedTeams() {
        // if (!$this->teamsManaged) $this->teamsManaged = Team::Managed($this);
        if (!$this->teamsManaged) $this->teamsManaged = TeamManagement::User($this);
        return $this->teamsManaged;
    }

    function getTeams() {
        return ($this->teams = Team::Following($this));
    }

    function getInvites() {
        $this->invites = TeamFollower::TreasurerInvites($this);
    }

    function getStripeCustomerIDs() {
        $sql = "SELECT * FROM stripeCustomers WHERE userID = :userID AND deleted IS NULL";
        $rlt = Database::Execute($sql, ["userID" => $this->id]);
        if (isset($rlt['success']['rows']) && $rlt['success']['rows']) {
            $this->getStripeCustomerIDs = $rlt['success']['rows'];
        }
        return $this->getStripeCustomerIDs;
    }

    function ProfileBasic() {
        return [
            "id" => $this->id,
            'name' => $this->__toString(),
            "firstname" => $this->firstname,
            "lastname" => $this->lastname,
            "email" => $this->email,
            "mobile" => $this->mobile,
            "line1" => $this->line1,
            "town" => $this->town,
            "postcode" => $this->postcode,
            "dob" => $this->dob,
            "isAdmin" => $this->isAdmin,
            "isManager" => $this->isManager,
            "isAuthor" => $this->isAuthor,
        ];
    }
    function Profile() {
        $this->getTeams();
        $this->getInvites();
        $teams = $invites = [];
        if ($this->teams) {
            foreach ($this->teams as $t) {
                try {
                    $teams[] = $t->ApiData();
                } catch (Exception $e) {
                    $teams[] = "{$t->id} " . $e->getMessage();
                }
            }
        }
        if ($this->invites) {
            foreach ($this->invites as $i) $invites[] = $i->TreasurerInvite_ApiData();
        }
        return [
            "id" => $this->id,
            "firstname" => $this->firstname,
            "lastname" => $this->lastname,
            "email" => $this->email,
            "mobile" => $this->mobile,
            "line1" => $this->line1,
            "town" => $this->town,
            "postcode" => $this->postcode,
            "dob" => $this->dob,
            "teams" => $teams,
            "invites" => $invites,

        ];
    }

    /* Getters */
    function getFirstname() {
        return $this->firstname;
    }
    function getLastname() {
        return $this->lastname;
    }
    function getEmail() {
        return $this->email;
    }
    function getMobile() {
        return $this->mobile;
    }

    /* Setters */
    function setPassword(String $password) {
        $this->password = password_hash($password, PASSWORD_DEFAULT);
        $sql = "UPDATE `users` SET `password` = :password WHERE `id` = :id";
        // Logging::Add($sql,true);
        $rlt = Database::Execute($sql, ["password" => $this->password, "id" => $this->id]);
        // Logging::Add("Password updated for {$this->email}",true);
        // Logging::Add($sql,true);
        return $rlt;
    }

    /* Statics */
    static function EmailLookup(String $email) {
        $sql = "SELECT * FROM `users` WHERE `email` = :email";
        // $rlt = new Db($sql,["email" => $email]);
        $rlt = Database::Execute($sql, ["email" => $email]);
        if ($rlt['success']['rows']) {
            $user = new static();
            $user->Load($rlt['success']['rows'][0]);
            return $user;
        }
    }

    static function Exists(String $email) {
        $sql = "SELECT * FROM `users` WHERE `email` = :email";
        $rlt = static::Query($sql, ["email" => $email]);
        return $rlt[0] ?? null;
    }

    static function fromEmail(String $email) {
        return static::EmailLookup($email);
    }

    static function fromMobile(String $mobile) {
        return static::Query("SELECT * FROM users WHERE mobile = :mobile", ["mobile" => $mobile]);
    }

    static function Authenticated() {

        return (isset($_SESSION['userID']) && $_SESSION['userID']) ? new static($_SESSION['userID']) : false;
    }

    static function AuthUserID() {
        return (isset($_SESSION['userID']) && $_SESSION['userID']) ? $_SESSION['userID'] : false;
    }

    static function AuthUser() {
        return (isset($_SESSION['userID']) && $_SESSION['userID']) ? new static($_SESSION['userID']) : false;
    }

    static function AuthUserEmail() {
        if (isset($_SESSION['userID']) && $_SESSION['userID']) {
            $user = new static($_SESSION['userID']);
            return $user->email;
        }
    }

    static function Live() {
        $sql = "SELECT * FROM `users` WHERE `deleted` IS NULL ORDER BY `firstname`, `lastname`, `email`";
        $db = new Db($sql);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $user = new static();
            $user->Load($r);
            (!$return) ? $return[] = $user : array_push($return, $user);
        }
        return $return;
    }

    static function Customers($liveOnly = true, $count = false) {
        $sql = "SELECT * FROM `users` WHERE `isAdmin` IS NULL";
        if ($liveOnly === true) $sql .= " AND `deleted` IS NULL";
        $db = new Db($sql);
        if (!$db->rows) return;

        if ($count) return count($db->rows);
        $return = [];
        foreach ($db->rows as $r) {
            $user = new \User();
            $user->Load($r);
            $user->Save();
            (!$return) ? $return[] = $user : array_push($return, $user);
        }
        return $return;
    }

    static function Coordinators($liveOnly = true) {
        $sql = "SELECT * FROM `users` WHERE `isAdmin` = 1 ORDER BY firstname, lastname";
        $db = new Db($sql);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $user = new \User();
            $user->Load($r);
            $user->Save();
            (!$return) ? $return[] = $user : array_push($return, $user);
        }
        return $return;
    }

    static function Search(String $filterText = null, bool $is_coordinator = false) {
        $sql = "SELECT * FROM users WHERE email IS NOT NULL";
        if ($filterText) {
            $filterText = trim($filterText); // Trim leading and trailing spaces from the filter text
            $sql .= " AND (CONCAT(TRIM(firstname), ' ', TRIM(lastname)) LIKE '%$filterText%' OR `email` LIKE '%$filterText%' OR `mobile` LIKE '%$filterText%')";
        }
        if ($is_coordinator) {
            $sql .= " AND isAdmin = 1";
        }
        $sql .= " ORDER BY COALESCE(`firstname`, `lastname`, `email`) ASC";
        return ["sql" => $sql, "data" => static::Query($sql)];
    }



    static function Captains($liveOnly = true) {
        $sql = "SELECT * from `users` where id IN (SELECT `captainID` FROM `teams`) ORDER BY firstname, lastname, email";
        $db = new Db($sql);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $user = new \User();
            $user->Load($r);
            $user->Save();
            (!$return) ? $return[] = $user : array_push($return, $user);
        }
        return $return;
    }

    static function Organisers($liveOnly = true) {
        $sql = "SELECT * from `users` where id IN (SELECT `captainID` AS `user` FROM `teams` UNION SELECT `treasurerID` AS `user` FROM `teams`)";
        $db = new Db($sql);
        if (!$db->rows) return;
        $return = [];
        foreach ($db->rows as $r) {
            $user = new \User();
            $user->Load($r);
            $user->Save();
            (!$return) ? $return[] = $user : array_push($return, $user);
        }
        return $return;
    }

    static function isAdmin() {
        $authUser = static::AuthUser();
        if (!$authUser) return;
        if ($authUser->isAdmin == 1 || $authUser->getEmail() == "<EMAIL>") return true;
    }

    static function isManager() {
        $authUser = static::AuthUser();
        if (!$authUser) return;
        if ($authUser->isManager == 1) return true;
    }

    static function isFinance() {
        $authUser = static::AuthUser();
        if (!$authUser) return;
        if ($authUser->isFinance == 1) return true;
    }

    static function isAuthor() {
        $authUser = static::AuthUser();
        if (!$authUser) return;
        if ($authUser->getEmail() == "<EMAIL>") return true;
    }

    // Hub
    public function hasRequiredRole() {
        return ($this->isAdmin == 1 || $this->isManager == 1 || $this->isFinance == 1 || $this->isAuthor == 1);
    }

    static function AdminActivation(Int $userID) {
        $sql = "UPDATE `users` SET `deleted` = NULL, `activationCode` = NULL, `activationIP` = 1, `activationStamp` = NOW() WHERE `id` = :userID";
        $db = new Db($sql, ["userID" => $userID]);
        if ($db->affectedRows == 1) return true;
    }

    static function AdminDeactivation(Int $userID) {
        $sql = "UPDATE `users` SET `activationCode` = NULL, `activationIP` = NULL, `activationStamp` = NULL WHERE `id` = :userID";
        $db = new Db($sql, ["userID" => $userID]);
        if ($db->affectedRows == 1) return true;
    }

    static function ClearReminders() {
        $sql = "UPDATE `users` SET `resetIP` = NULL, `resetSession` = NULL, resetCode = NULL, resetExpiry = NULL WHERE `resetExpiry` IS NOT NULL AND `resetExpiry` <= NOW()";
        Database::Execute($sql);
    }

    static function League(League $league) {
        $sql = "SELECT 
            users.* FROM users LEFT JOIN teams ON users.id = teams.captainID 
            UNION
            SELECT users.* FROM users LEFT JOIN teams ON users.id = teams.treasurerID 
            WHERE users.deleted IS NULL AND teams.`leagueID` = {$league->id}
            ORDER BY `firstname`, `lastname`,`email`";
        // echo $sql;
        $db = new Db($sql);
        if (!$db->rows) return [];
        foreach ($db->rows as $r) {
            $user = new static();
            $user->Load($r);
            $return[] = $user;
        }
        return $return;
    }

    static function Create(array $data = []) {
        /* Returns STRING error or User object */
        if (!$data) return "No user data provided";
        if (!isset($data['email']) || !$data['email'] || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            return "Missing or Invalid Email {$data['email']}";
        }
        $user = new static();
        $user->Load($data);
        $user->Save();
        return $user;
    }

    static function Register(array $data = []) {
        /* Returns STRING error or User object */
        $return = ["success" => null, "error" => null];
        if (!$data) {
            $return["error"] = "No user data provided";
            return $return;
        }

        // Validate required fields
        if (!isset($data['email']) || !$data['email'] || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $return["error"] = "Missing or Invalid Email";
            return $return;
        }
        if (!isset($data['firstname']) || !trim($data['firstname'])) {
            $return["error"] = "Missing or Invalid First Name";
            return $return;
        }
        if (!isset($data['lastname']) || !trim($data['lastname'])) {
            $return["error"] = "Missing or Invalid Last Name";
            return $return;
        }
        if (!isset($data['password']) || !$data['password'] || strlen($data['password']) < 6) {
            $return["error"] = "Password must be at least 6 characters long";
            return $return;
        }

        // Check if user already exists
        $existingUser = static::EmailLookup($data['email']);
        if ($existingUser) {
            $return["error"] = "An account with this email address already exists";
            return $return;
        }

        $user = new static();
        $user->Load($data);
        // Set the password properly (will be hashed in setPassword method)
        $user->setPassword($data['password']);
        $rlt = $user->Save();
        ($user->id) ? $return["success"] = $user : $return["error"] = $rlt;
        return $return;
    }

    static function SearchCreate(array $data = []) {
        /* Must have a VALID Email */
        if (!isset($data['email']) || !$data['email'] || !filter_var($data['email'], FILTER_VALIDATE_EMAIL)) return "Missing or invalid email";
        // Lookup User from supplied email address
        $user1 = User::fromEmail($data['email']);
        $user2 = (isset($data['mobile']) && $data['mobile']) ? User::fromMobile($data['mobile']) : null;
        if ($user1 && is_object($user1) && $user1->id) {
            // Go with this, updating mobile is it's new info
            if (!$user1->mobile && !$user2 && isset($data['mobile'])) {
                $user1->mobile = $data['mobile'];
                $user1->Save();
            }
            return $user1;
        }
        if ($user2 && is_object($user2) && $user2->id) {
            // Update user record with email address
            $user2->email = $data['email'];
            $user2->Save();
            return $user2;
        }
        // Nothing found, new user required
        return static::Create($data);
    }

    static function TeamCaptain(Team $team) {
        $sql = "SELECT users.id, users.firstname, users.lastname, users.email, users.mobile FROM teamFollowers LEFT JOIN users ON teamFollowers.userID = users.id WHERE users.deleted IS NULL AND teamFollowers.deleted IS NULL AND teamFollowers.isCaptain = 1 AND `teamFollowers`.`teamID` = {$team->id}";
        $rlt = static::Query($sql);
        return ($rlt) ? $rlt[0] : null;
    }

    static function TeamTreasurer(Team $team) {
        $sql = "SELECT users.id, users.firstname, users.lastname, users.email, users.mobile FROM teamFollowers LEFT JOIN users ON teamFollowers.userID = users.id WHERE users.deleted IS NULL AND teamFollowers.deleted IS NULL AND teamFollowers.isTreasurer = 1 AND `teamFollowers`.`teamID` = {$team->id}";
        $rlt = static::Query($sql);
        return ($rlt) ? $rlt[0] : null;
    }

    static function TeamSquad() {
    }

    static function Birthdays(Int $month) {
        $sql = "SELECT * FROM users WHERE dob IS NOT NULL AND DATE_FORMAT(dob,'%m') = :month ORDER BY DATE_FORMAT(dob,'%d-%m') ASC";
        if ($month < 10) $month = "0$month";
        return static::Query($sql, ["month" => $month]);
    }

    public function ApiListOutput($id) {
        $data = $this->ApiData();

        // Only list teams where the user is captain or treasurer (not just following)
        $sql = "SELECT t.name as team_name, l.id as league_id, l.name as league_name, tf.isCaptain, tf.isTreasurer
                FROM teams t
                INNER JOIN teamFollowers tf ON t.id = tf.teamID
                INNER JOIN leagues l ON t.leagueID = l.id
                WHERE tf.userID = :userID AND t.deleted IS NULL AND tf.deleted IS NULL
                  AND (tf.isCaptain = 1 OR tf.isTreasurer = 1)";
        $rows = Database::Execute($sql, ['userID' => $this->id]);
        $teamNames = [];
        $leaguesAssoc = [];

        $data['is_captain'] = false;
        $data['is_treasurer'] = false;

        if (!empty($rows['success']['rows'])) {
            foreach ($rows['success']['rows'] as $row) {
                $teamNames[] = $row['team_name'];
                $leaguesAssoc[$row['league_id']] = [
                    'id' => $row['league_id'],
                    'name' => $row['league_name']
                ];
                // if the first item is true don't override it
                if (!$data['is_captain'] && $row['isCaptain'] == 1) {
                    $data['is_captain'] = true;
                }
                if (!$data['is_treasurer'] && $row['isTreasurer'] == 1) {
                    $data['is_treasurer'] = true;
                }
            }
        }

        $data['teams'] = array_values(array_unique($teamNames));
        $data['leagues'] = array_values($leaguesAssoc);

        return $data;
    }
}
