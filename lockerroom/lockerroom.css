@import url('https://fonts.googleapis.com/css2?family=Cabin:wght@400;700&family=Oxygen:wght@400;700&display=swap');

:root {
    /* --dark-blue: #171e37; */
    --dark-blue: #262626;
    --dark-purple: #760890;
    --dark-grey: #444;
    --dark-pink: #FF1493;
    --dark-red: #cc0000;
    --dark-orange: #ff7a16;
    --dark-green: green;
    --dark-yellow: rgba(225, 180, 1);
    --dark-beige: #F5F1E8;
    --light-green: #00b6ac;
    /* #20c997 */
    --light-grey: #ddd;
    --blue: #007bff;
    --teal: #20c997;
    --font-primary: 'neue-kabel', 'Oxygen', sans-serif
}

.app,
header {
    /* max-width: 1000px;
    margin: 0 auto; */
}

.max-600 {
    max-width: 600px;
}

.w-90 {
    width: 90%;
}

.text-white {
    color: white;
}

.text-green {
    color: var(--dark-green);
}

.text-red {
    color: var(--dark-red);
}

.text-centre,
.text-center {
    text-align: center;
}

/* Password Strength Indicators */
.password-strength .strength-weak {
    background-color: #ffebee;
    color: #d32f2f;
    border: 1px solid #ffcdd2;
}

.password-strength .strength-medium {
    background-color: #fff3e0;
    color: #f57c00;
    border: 1px solid #ffcc02;
}

.password-strength .strength-strong {
    background-color: #e8f5e8;
    color: #2e7d32;
    border: 1px solid #4caf50;
}

/* Loading spinner animation */
@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

/* Disabled button styles */
.button:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.text-right {
    text-align: right;
}

.divisionTable,
.fixtureTable,
.resultsTable {
    border-collapse: collapse;
    width: 100%;
    max-width: 600px;
    font-size: .8em;
}

.hidden {
    display: none;
}

.divisionTable tbody tr:nth-child(even),
.fixtureTable tbody tr:nth-child(even),
.resultsTable tbody:nth-child(even) {
    background-color: #f2f2f2;
}

.bg-dark-green {
    background-color: var(--dark-green);
}

.bg-dark-red {
    background-color: var(--dark-red);
}

.bg-dark-blue {
    background-color: var(--dark-blue);
}

.bg-dark-orange {
    background-color: var(--dark-orange);
}

.bg-dark-pink {
    background-color: var(--dark-orange);
    ;
    color: white;
}

.messages {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 15;
}

.messages .message,
.helpInfo {
    padding: 1em;
}

/* Resets */
* {
    margin: 0;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

th {
    text-align: left;
}

button {
    border: none;
    background-color: transparent;
    cursor: pointer;
    width: auto;
    font-family: 'neue-kabel', 'Oxygen', sans-serif;
}

.button {
    padding: .75em 1em;
    border-radius: .5em;
    margin: .5em 0;
}

.button-md {
    padding: 0.5em 0.75em;
}

.button-sm {
    padding: 0.5em;
}

.button:disabled {
    cursor: no-drop;
    opacity: .5;
}

h1,
p {
    color: var(--dark-blue);
}

h1 {
    font-family: 'neue-haas-grotesk-display', 'Oxygen', sans-serif;
    font-style: italic;
    text-transform: capitalize;
}

p {
    font-size: .9em;
    font-family: 'neue-kabel', 'Oxygen', sans-serif;
}

p,
form {
    margin-top: 1em;
}

/* .team-management {
    max-width: 1000px;
    margin: 0 auto;
} */

.statementTable {
    margin-top: 1rem;
}

.statementTable td,
.statementTable th {
    padding: .25em .25em;
    font-size: .8em;
}

.button-green {
    background-color: var(--dark-green);
    color: white;
}

.button-orange {
    background-color: var(--dark-orange);
    /* color: white; */
}

.button-purple {
    background-color: var(--dark-purple);
    color: white;
}

.button-dark-blue {
    background-color: var(--dark-blue);
    color: white;
}

.followUnfollow {
    padding: 0.75em 0.75em;
}

.button-blue-outer {
    border: thin solid var(--dark-blue);
    color: var(--dark-blue);
}

.button-red-outer {
    border: thin solid var(--dark-red);
    color: var(--dark-red);
}

.button-dark-grey {
    background-color: var(--dark-grey);
    color: white;
}

.button-blue,
.teamData-division-btn.selected,
.teamData-view-option.selected {
    background-color: var(--dark-blue);
    color: white;
}

.button-sm {
    padding: 0.35em .7em;
    font-size: .75em;
}

.fa-spinner {
    animation: spin 2s linear infinite;
}

@keyframes spin {
    100% {
        -webkit-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

.button-pink,
.teamData-division-btn,
.teamData-view-option {
    /* background-color: var(--dark-orange);; */
    background-color: var(--dark-orange);
    ;
    color: white;
}

.teamData-division-btn {
    font-size: .6em;
}

.mt-1 {
    margin-top: .25rem;
}

.mt-2 {
    margin-top: .5rem;
}

.mt-3 {
    margin-top: .75rem;
}

.mt-4 {
    margin-top: 1rem;
}

.ml-1 {
    margin-left: .25rem;
}

.ml-2 {
    margin-left: .5rem;
}

.ml-3 {
    margin-left: .75rem;
}

.ml-4 {
    margin-left: 1rem;
}

.mr-1 {
    margin-right: .25rem;
}

.mr-2 {
    margin-right: .5rem;
}

.mr-3 {
    margin-right: .75rem;
}

.mr-4 {
    margin-right: 1rem;
}

.mx-1 {
    margin-left: .25rem;
    margin-right: .25rem;
}

.mx-2 {
    margin-left: .5rem;
    margin-right: .5rem;
}

.mx-3 {
    margin-left: .75rem;
    margin-right: .75rem;
}

.mx-4 {
    margin-left: 1rem;
    margin-right: 1rem;
}

.my-1 {
    margin-top: .25rem;
    margin-bottom: .25rem;
}

.my-2 {
    margin-top: .5rem;
    margin-bottom: .5rem;
}

.my-3 {
    margin-top: .75rem;
    margin-bottom: .75rem;
}

.my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem;
}

::-webkit-scrollbar {
    width: 10px;
}

/* Track */
::-webkit-scrollbar-track {
    background: #f1f1f1;
}

/* Handle */
::-webkit-scrollbar-thumb {
    background: #888;
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #555;
}

body {
    font-family: var(--font-primary);
    background-color: var(--dark-beige);
}

#app {
    max-width: 1200px;
    margin: 0 auto;
    position: relative;
    background-color: var(--dark-beige);
}

label {
    /* display: none; */
    font-size: .8em;
}

.text-white {
    color: white;
}

select,
input,
textarea {
    padding: .75em .75em;
    border: thin solid var(--light-grey);
    border-radius: .5em;
    font-family: Arial, Helvetica, sans-serif;
    display: block;
    /* min-width: 300px; */
    font-family: var(--font-primary);
}

header {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    /* background-image: url('img/lr_bg_square.png');
    background-size: 100% 100%;
    background-repeat: no-repeat; */
    height: 30vh;
}

@media screen and (min-width:1000px) {
    .bg_lg {
        display: block !important;
        width: 100%;
    }

    .bg_sm {
        display: none;
    }
}

.bg_sm {
    width: 100%;
}

.bg_lg {
    display: none;
}

.paymentDocuments {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.paymentOptions {
    display: flex;
}

.paymentOption {
    cursor: pointer;
    width: 100px;
    display: flex;
    flex-direction: column;
    padding: 1em 1em;
    justify-content: space-around;
    align-items: center;
    border-radius: .5em;
    transition: transform 300ms linear;
    position: relative;
}

.paymentOption p {
    color: white;
    font-weight: bold;
    pointer-events: none;
}

.paymentOption p.paymentOption-selected {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    margin: .15em;
    text-align: right;
    font-size: 1.2em;
}

.paymentOption:hover {
    transform: scale(1.1);
}

.paymentOption-weekly {
    margin-right: 1em;
}

@media screen and (min-width:1000px) {
    header {
        /* background-image: url('img/lr_bg_rect.png'); */
    }
}

.topNav {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    z-index: 5;
    position: relative;
}

.screens {
    position: relative;
    z-index: 5;
}

.screen1 {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    height: 100vh;
}

.screen {
    /* position: relative; */
    margin-top: 15vh;
    padding-bottom: 120px;
}

.screen1 .topHalf,
.screen1 .bottomHalf {
    height: 50%;
}

.screen1 .topHalf {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    padding: 2em;
    /* width: 40%; */
}

.screen1 .topHalf h1 {
    font-size: 4em;
    line-height: 1;
    margin-top: 1em;
    font-family: 'Cabin', sans-serif;
    max-width: 50vw;
}

.screen1 .enter-league-team {
    background-color: var(--dark-blue);
    height: 100px;
    width: 300px;
    margin: 0 auto;
    border-radius: .5em;
    padding: 1em;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -ms-flex-pack: distribute;
    justify-content: space-around;
    position: relative;
}

.screen1 .enter-league-team::after {
    content: '';
    position: absolute;
    border: 0.5em solid transparent;
    border-bottom: 2em solid var(--dark-blue);
    top: -2em;
    left: 40%;
    -webkit-transform: skewX(20deg);
    -ms-transform: skewX(20deg);
    transform: skewX(20deg);
    width: .25em;
}

.screen1 .enter-league-team-text {
    font-size: 1rem;
    color: white;
    text-align: center;
}

.screen1 .enter-league-team-button {
    background-color: var(--dark-orange);
    ;
    border: none;
    border-radius: .5em;
    color: white;
    padding: .5em;
    width: 150px;
    margin: 0 auto;
}

.details,
.paymentDetails {
    display: flex;
}

.container {
    padding: 1em;
}

.profile-details {
    display: grid;
    width: 100%;
    grid-template-columns: 1fr 2fr;
    align-items: center;
}

.treasurer-invite-buttons {
    display: flex;
    margin-top: 1rem;
}

.treasurer-invite-decline-button {
    margin-left: 1rem;
}

.profile-details input {
    background-color: transparent;
}

.pagination-terms {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 300px;
}

.profile-details .screen2 h2,
.screen3 h2,
.screen4 h2,
.screen5 h2,
.screen6 h2 {
    margin: 0 1rem;
    padding: 0 0 .5rem 0;
    border-bottom: thin solid var(--dark-blue);
    max-width: 50vw;
}

.screen3 .documents {
    padding: 1rem;
}

.screen2 input {
    border: none;
    padding: .65em;
    border-radius: .25em;
}

.screen2 input::-webkit-input-placeholder {
    color: #aaa;
    font-style: italic;
}

.screen2 input::-moz-placeholder {
    color: #aaa;
    font-style: italic;
}

.screen2 input:-ms-input-placeholder {
    color: #aaa;
    font-style: italic;
}

.screen2 input::-ms-input-placeholder {
    color: #aaa;
    font-style: italic;
}

.screen2 input::placeholder {
    color: #aaa;
    font-style: italic;
}

.screen2 .profile-form {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}

.screen2 .profile-form .profile-group {
    margin: .25em auto;
    width: 90%;
}

.screen2 .profile-form .profile-group-input {
    width: 100%;
    text-align: center;
    color: var(--dark-blue);
    font-weight: bold;
}

.screen2 .profile-form .profile-input-button {
    background-color: var(--dark-orange);
    ;
    color: var(--dark-blue);
    border: none;
    width: 100px;
    margin: 1em auto;
    padding: .5em 1em;
    border-radius: .5em;
}

.screen3 .document-group {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}

.screen3 .document-item {
    background-color: var(--dark-orange);
    ;
    color: white;
    margin: .5em 0;
    max-width: 400px;
    border-radius: .5em;
    padding: .5em;
    text-decoration: none;
}

/* New Team | Screen 6 */
.screen6 .leagues {
    padding: 1em;
}

.screen6 .leagues .league {
    margin: .25em 0;
}

.search-options {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: justify;
    -ms-flex-pack: justify;
    justify-content: space-between;
}

.search-option {
    -webkit-box-flex: 1;
    -ms-flex: 1;
    flex: 1;
}

.search-option-local {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}

.team-view-row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
}

.newteam-form {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}

.newteam-form input {
    margin-top: 1em;
    width: 300px;
}

.newteam-form i {
    font-size: 1.25em;
    margin-left: .75em;
}

.admin-alert {
    content: '\0021';
    color: white;
    position: absolute;
    right: .5em;
    top: -.75em;
    width: 1.5em;
    height: 1.5em;
    border-radius: 100%;
    background-color: red;
    font-weight: bold;
}

.newteam-form i.show {
    display: inline-block;
}

.newteam-league-option {
    padding: .5em;
    cursor: pointer;
}

.newteam-form-group {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}

.newteam-form-group button {
    -ms-flex-item-align: start;
    align-self: flex-start;
    margin-top: 1em;
}

.payment-cards {
    margin-top: 1rem;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}

.payment-card {
    display: -ms-grid;
    display: grid;
    -ms-grid-columns: 1fr 4fr 1fr 1fr;
    grid-template-columns: 1fr 4fr 1fr 1fr;
}

.payment-card i {
    cursor: pointer;
}

.teams {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
}

.team {
    display: -ms-grid;
    display: grid;
    -ms-grid-columns: 4fr 4fr 1fr;
    grid-template-columns: 4fr 4fr 1fr;
}

.team i {
    cursor: pointer;
}

.divisionTable thead,
.resultsTable thead,
.fixtureTable thead {
    /* background-color: var(--light-green); */
    background-color: var(--dark-blue);
    color: white;
}

.teamSelections {
    margin-bottom: 1.5em;
}

.staffDetails,
.paymentDetails {
    display: flex;
    flex-direction: column;
    margin: .5em 0;

}

.details-captain,
.details-treasurer,
.details-payment {
    justify-content: space-between;
    align-items: center;
}

.details-captain p,
.details-treasurer p,
.details-payment p {
    font-size: 1.1em;
    margin: 0;

}

.divisionTable thead th,
.fixtureTable thead th,
.resultsTable thead th {
    font-weight: normal;
    padding: .25em;
}

.divisionTable tbody td,
.fixtureTable tbody td,
.resultsTable tbody td {
    padding: .25em;
}

footer {
    background-color: #171e37;
    background-color: var(--dark-blue);
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    height: 100px;
    position: fixed;
    left: 0;
    bottom: 0;
    width: 100%;
    z-index: 10;
}

.bottom-nav {
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
    height: 100%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-pack: distribute;
    justify-content: space-around;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
}

.bottom-nav .bottom-nav-link {
    color: white;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
    -ms-flex-direction: column;
    flex-direction: column;
    -webkit-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    position: relative;
    width: 5em;
    text-decoration: none;
    background-color: transparent;
    border: none;
}

.bottom-nav .bottom-nav-link.active i {
    color: white;
}

.bottom-nav .bottom-nav-link.active i::after {
    background-color: var(--dark-orange);
    ;
}

.bottom-nav i::after {
    content: '';
    position: absolute;
    background-color: white;
    top: -.04em;
    left: .575em;
    height: 1.75em;
    width: 1.75em;
    border-radius: 50%;
    z-index: -1;
}

.bottom-nav i {
    margin: .25em 0;
    font-size: 1.75em;
    color: var(--dark-blue);
}

.bottom-nav .legend {
    margin: 0.75em 0 0 0;
    font-size: .9em;
}

.divisionSelection {
    display: flex;
    align-items: center;
}

/* The switch - the box around the slider */
.switch {
    position: relative;
    display: inline-block;
    width: 44px;
    height: 22px;
}

/* Hide default HTML checkbox */
.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

/* The slider */
.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--dark-orange);
    ;
    -webkit-transition: .4s;
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 15px;
    width: 15px;
    left: 5px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
}

input:checked+.slider {
    background-color: var(--dark-blue);
}

input:focus+.slider {
    box-shadow: 0 0 1px var(--dark-blue);
}

input:checked+.slider:before {
    -webkit-transform: translateX(18px);
    -ms-transform: translateX(18px);
    transform: translateX(18px);
}

/* Rounded sliders */
.slider.round {
    border-radius: 24px;
}

.slider.round:before {
    border-radius: 50%;
}

/* Spinner */
.smt-spinner {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    border-right: 4px solid white;
    border-top: 4px solid transparent;
    border-left: 4px solid transparent;
    border-bottom: 4px solid transparent;
    animation: rotate--spinner 1.6s infinite;
}


@keyframes rotate--spinner {
    from {
        transform: rotate(0);
    }

    to {
        transform: rotate(360deg);
    }
}

#terms-and-conditions {
    max-width: 600px;
}

/* Mobile for terms-and-conditions */
@media screen and (max-width: 600px) {
    #terms-and-conditions {
        padding: 0 1em;
        max-width: 100%;
    }
}